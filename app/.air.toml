root = "."
tmp_dir = "tmp"

[build]
  args_bin = ["--mode", "all"]
  bin = "./bin/signalsd"
  cmd = "swag init -g ./cmd/signalsd/main.go && sqlc generate && templ generate && go build -o ./bin/signalsd ./cmd/signalsd/main.go"
  delay = 1000
  exclude_dir = ["tmp", "internal/database", "docs"]
  exclude_file = []
  exclude_regex = [".*_templ.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "templ", "sql"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_error = true

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false

[proxy]
  enabled = true
  proxy_port = 8383
  app_port = 8282

