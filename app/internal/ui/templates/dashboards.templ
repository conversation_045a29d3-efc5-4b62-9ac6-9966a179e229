package templates
// =============================================================================
// MAIN DASHBOARD PAGE
// =============================================================================
templ DashboardPage() {
	@BaseLayout("Dashboard") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Dashboard</h1>
			<p class="text-muted mb-6">Welcome to the Signalsd management interface.</p>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Signal Management</h3>
						<p class="text-muted mb-4">Search and manage signals across Information Sharing Networks.</p>
						<a href="/search" class="btn btn-secondary">
							Search Signals
						</a>
					</div>
				</div>
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">API Documentation</h3>
						<p class="text-muted mb-4">View the complete API reference and interactive documentation.</p>
						<a href="/docs" target="_blank" class="btn btn-secondary">
							View Docs ↗
						</a>
					</div>
				</div>
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Information Sharing Networks</h3>
						<p class="text-muted mb-4">Manage ISNs and configure data sharing.</p>
						<a href="/admin" class="btn btn-secondary">
							Manage ISNs
						</a>
					</div>
				</div>
			</div>
			<div id="dashboard-error">
			</div>
		</div>
	}
}

// =============================================================================
// ADMIN DASHBOARD PAGE
// =============================================================================
templ AdminDashboardPage() {
	@BaseLayout("ISN Administration") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">ISN Administration</h1>
			<p class="text-muted mb-6">Manage Information Sharing Networks and user access.</p>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<!-- Account Management -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Account Management</h3>
						<p class="text-muted mb-4">Add and manage user access to ISNs.</p>
						<a href="/admin/isn-accounts" class="btn btn-primary">Manage ISN Accounts</a>
					</div>
				</div>

				<!-- ISN Configuration -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">ISN Configuration</h3>
						<p class="text-muted mb-4">Create and configure Information Sharing Networks.</p>
						<button class="btn btn-secondary" disabled>Create ISN</button>
						<p class="text-xs text-muted mt-2">Coming soon</p>
					</div>
				</div>

				<!-- Signal Types -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Signal Types</h3>

						<p class="text-muted mb-4">Configure the signal types used in an ISN.</p>
						<a href="/signal-types" class="btn btn-primary">Manage Signal Types</a>

					</div>
				</div>

				<!-- User Management -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">User Management</h3>
						<p class="text-muted mb-4">Manage user accounts and permissions.</p>
						<button class="btn btn-secondary" disabled>Manage Users</button>
						<p class="text-xs text-muted mt-2">Coming soon</p>
					</div>
				</div>

				<!-- Service Accounts -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Service Accounts</h3>
						<p class="text-muted mb-4">Manage API service accounts and credentials.</p>
						<button class="btn btn-secondary" disabled>Manage Service Accounts</button>
						<p class="text-xs text-muted mt-2">Coming soon</p>
					</div>
				</div>

				<!-- System Settings -->
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">System Settings</h3>
						<p class="text-muted mb-4">Configure system-wide settings and preferences.</p>
						<button class="btn btn-secondary" disabled>System Settings</button>
						<p class="text-xs text-muted mt-2">Coming soon</p>
					</div>
				</div>
			</div>

		</div>
	}
}
