// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.943
package templates

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

// =============================================================================
// MAIN DASHBOARD PAGE
// =============================================================================
func DashboardPage() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			templ_7745c5c3_Err = Navigation().Render(ctx, templ_7745c5c3_Buffer)
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, " <div class=\"page-container\"><h1 class=\"page-title\">Dashboard</h1><p class=\"text-muted mb-6\">Welcome to the Signalsd management interface.</p><div class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\"><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">Signal Management</h3><p class=\"text-muted mb-4\">Search and manage signals across Information Sharing Networks.</p><a href=\"/search\" class=\"btn btn-secondary\">Search Signals</a></div></div><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">API Documentation</h3><p class=\"text-muted mb-4\">View the complete API reference and interactive documentation.</p><a href=\"/docs\" target=\"_blank\" class=\"btn btn-secondary\">View Docs ↗</a></div></div><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">Information Sharing Networks</h3><p class=\"text-muted mb-4\">Manage ISNs and configure data sharing.</p><a href=\"/admin\" class=\"btn btn-secondary\">Manage ISNs</a></div></div></div><div id=\"dashboard-error\"></div></div>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return nil
		})
		templ_7745c5c3_Err = BaseLayout("Dashboard").Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

// =============================================================================
// ADMIN DASHBOARD PAGE
// =============================================================================
func AdminDashboardPage() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var3 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var3 == nil {
			templ_7745c5c3_Var3 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var4 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			templ_7745c5c3_Err = Navigation().Render(ctx, templ_7745c5c3_Buffer)
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, " <div class=\"page-container\"><h1 class=\"page-title\">ISN Administration</h1><p class=\"text-muted mb-6\">Manage Information Sharing Networks and user access.</p><div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"><!-- Account Management --><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">Account Management</h3><p class=\"text-muted mb-4\">Add and manage user access to ISNs.</p><a href=\"/admin/isn-accounts\" class=\"btn btn-primary\">Manage ISN Accounts</a></div></div><!-- ISN Configuration --><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">ISN Configuration</h3><p class=\"text-muted mb-4\">Create and configure Information Sharing Networks.</p><button class=\"btn btn-secondary\" disabled>Create ISN</button><p class=\"text-xs text-muted mt-2\">Coming soon</p></div></div><!-- Signal Types --><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">Signal Types</h3><p class=\"text-muted mb-4\">Configure the signal types used in an ISN.</p><a href=\"/signal-types\" class=\"btn btn-primary\">Manage Signal Types</a></div></div><!-- User Management --><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">User Management</h3><p class=\"text-muted mb-4\">Manage user accounts and permissions.</p><button class=\"btn btn-secondary\" disabled>Manage Users</button><p class=\"text-xs text-muted mt-2\">Coming soon</p></div></div><!-- Service Accounts --><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">Service Accounts</h3><p class=\"text-muted mb-4\">Manage API service accounts and credentials.</p><button class=\"btn btn-secondary\" disabled>Manage Service Accounts</button><p class=\"text-xs text-muted mt-2\">Coming soon</p></div></div><!-- System Settings --><div class=\"card\"><div class=\"card-body\"><h3 class=\"card-title\">System Settings</h3><p class=\"text-muted mb-4\">Configure system-wide settings and preferences.</p><button class=\"btn btn-secondary\" disabled>System Settings</button><p class=\"text-xs text-muted mt-2\">Coming soon</p></div></div></div></div>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return nil
		})
		templ_7745c5c3_Err = BaseLayout("ISN Administration").Render(templ.WithChildren(ctx, templ_7745c5c3_Var4), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
