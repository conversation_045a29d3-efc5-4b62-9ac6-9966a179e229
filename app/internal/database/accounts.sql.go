// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: accounts.sql

package database

import (
	"context"

	"github.com/google/uuid"
)

const CreateServiceAccountAccount = `-- name: CreateServiceAccountAccount :one
INSERT INTO accounts (id, created_at, updated_at, account_type, is_active)
VALUES ( gen_random_uuid(), NOW(), NOW(), 'service_account', true)
RETURNING id, created_at, updated_at, account_type, is_active
`

func (q *Queries) CreateServiceAccountAccount(ctx context.Context) (Account, error) {
	row := q.db.QueryRow(ctx, CreateServiceAccountAccount)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccountType,
		&i.IsActive,
	)
	return i, err
}

const CreateUserAccount = `-- name: CreateUserAccount :one
INSERT INTO accounts (id, created_at, updated_at, account_type, is_active)
VALUES ( gen_random_uuid(), NOW(), NOW(), 'user', true)
RETURNING id, created_at, updated_at, account_type, is_active
`

func (q *Queries) CreateUserAccount(ctx context.Context) (Account, error) {
	row := q.db.QueryRow(ctx, CreateUserAccount)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccountType,
		&i.IsActive,
	)
	return i, err
}

const DisableAccount = `-- name: DisableAccount :execrows
UPDATE accounts SET (updated_at, is_active) = (NOW(), false)
WHERE id = $1
`

func (q *Queries) DisableAccount(ctx context.Context, id uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, DisableAccount, id)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const EnableAccount = `-- name: EnableAccount :execrows
UPDATE accounts SET (updated_at, is_active) = (NOW(), true)
WHERE id = $1
`

func (q *Queries) EnableAccount(ctx context.Context, id uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, EnableAccount, id)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const GetAccountByID = `-- name: GetAccountByID :one
SELECT
    a.id ,
    a.account_type,
    a.is_active,
    COALESCE(u.user_role, 'member') AS account_role
FROM
    accounts a
LEFT OUTER JOIN users u
ON a.id = u.account_id
WHERE a.id = $1
`

type GetAccountByIDRow struct {
	ID          uuid.UUID `json:"id"`
	AccountType string    `json:"account_type"`
	IsActive    bool      `json:"is_active"`
	AccountRole string    `json:"account_role"`
}

// return the account and user_role (user_role is not applicable to service_accounts - which are always treated as members - so just return 'member' in these cases)
// returns both active and inactive accounts
func (q *Queries) GetAccountByID(ctx context.Context, id uuid.UUID) (GetAccountByIDRow, error) {
	row := q.db.QueryRow(ctx, GetAccountByID, id)
	var i GetAccountByIDRow
	err := row.Scan(
		&i.ID,
		&i.AccountType,
		&i.IsActive,
		&i.AccountRole,
	)
	return i, err
}
