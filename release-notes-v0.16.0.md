# Release v0.16.0

## What's new

### 🔧 **Infrastructure & Platform Updates**

**Go 1.25.0 Upgrade:**
- Updated to Go 1.25.0 across all components and CI/CD pipelines
- Updated Docker images to use `golang:1.25-alpine` base image
- Enhanced build performance and security with latest Go runtime

**Logging System Overhaul:**
- **Breaking Change**: Migrated from `rs/zerolog` to Go's standard `log/slog` library
- Improved structured logging with better context awareness
- Enhanced request logging middleware with cleaner log output
- Better integration with Go's standard library ecosystem

### 🎨 **User Interface Enhancements**

**New Signal Types Management:**
- Added comprehensive Signal Types administration page
- Create new signal types directly through the UI
- Enhanced GitHub URL validation for signal type schemas
- Improved error handling and user feedback throughout the UI

**UI Architecture Improvements:**
- Refactored UI codebase with clearer filesystem layout
- Enhanced authentication flow with automatic token refresh
- Improved cookie-based session management
- Better separation of concerns between UI and API components

**Enhanced Error Handling:**
- Improved error messages and user feedback
- Better handling of authentication failures
- Enhanced validation for form inputs and API requests

### 🔐 **Authentication & Security**

**Token Management Improvements:**
- Enhanced JWT token handling with better expiration checks
- Improved refresh token flow for seamless user experience
- Better cookie security configuration
- Enhanced session management across UI components

**Access Control Refinements:**
- Improved ISN permission validation
- Enhanced admin access controls
- Better handling of authentication state transitions

### 🐛 **Bug Fixes and Improvements**

**Database & Queries:**
- Enhanced SQL query documentation and clarity
- Improved filtering to only return active ISNs and signal types (`is_in_use = true`)
- Better handling of correlation ID updates
- Enhanced signal withdrawal and reactivation logic

**API & Backend:**
- Fixed cookie attribute configuration issues
- Improved CORS handling with updated library (v0.8.0 → v0.9.0)
- Enhanced request/response logging
- Better error handling in signal processing

**Development & Testing:**
- Refactored integration tests for better organization
- Split tests into focused modules (OAuth, signals, CORS)
- Enhanced test coverage for authentication flows
- Improved CI/CD pipeline reliability

### 📦 **Dependency Updates**

- `github.com/jub0bs/cors`: v0.8.0 → v0.9.0
- `github.com/go-chi/chi/v5`: v5.2.2 → v5.2.3
- `golang.org/x/crypto`: v0.40.0 → v0.41.0
- `golang.org/x/text`: v0.28.0 → v0.29.0
- `golang.org/x/time`: v0.12.0 → v0.13.0
- `github.com/spf13/cobra`: v1.9.1 → v1.10.1

### 🔧 **Developer Experience**

**Build & Deployment:**
- Fixed Docker Compose configuration issues
- Enhanced Makefile with better Docker integration
- Improved local development setup options
- Better version information handling in builds

**Code Quality:**
- Removed debug logging statements for cleaner production logs
- Enhanced code organization and maintainability
- Better component naming and structure
- Improved documentation throughout the codebase

## Breaking Changes

⚠️ **Logging Library Migration**: The switch from `zerolog` to `slog` may affect any custom logging integrations or log parsing tools that depend on the previous log format.

## Migration Notes

- **Logging**: If you have custom log parsing or monitoring tools, you may need to update them to handle the new `slog` format
- **Dependencies**: Ensure your deployment environment supports Go 1.25.0
- **Configuration**: No configuration changes required for standard deployments

## Technical Details

This release includes significant infrastructure improvements with the migration to Go's standard logging library and the upgrade to Go 1.25.0. The UI has been substantially enhanced with new management capabilities and better user experience. The authentication system has been refined for better security and usability.

The codebase has been reorganized for better maintainability, and the test suite has been expanded and restructured for better coverage and organization.
