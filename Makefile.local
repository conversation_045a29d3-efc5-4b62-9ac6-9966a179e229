# Makefile for signalsd using locally installed go tools
# Simple automation for common development tasks

DATABASE_URL = "postgres://$(USER):@localhost:5432/signalsd_admin?sslmode=disable"

.PHONY: help psql check generate docs swag-fmt sqlc fmt vet lint security test clean


# Default target - show available commands
help:
	@echo "Available commands:"
	@echo "  make check     - Run all pre-commit checks (recommended before committing)"
	@echo "  make generate  - Generate docs and code (swagger + sqlc + templ)"
	@echo "  make docs      - Generate swagger documentation"
	@echo "  make swag-fmt  - format swag comments"
	@echo "  make sqlc      - Generate sqlc code"
	@echo "  make templ     - Generate templ templates" 
	@echo "  make fmt       - Format code"
	@echo "  make vet       - Run go vet"
	@echo "  make lint      - Run staticcheck"
	@echo "  make security  - Run gosec security analysis"
	@echo "  make test      - Run tests"
	@echo "  make psql      - run psql agaist the dev database"
	@echo "  make ui-build  - Build UI binary"
	@echo "  make clean     - Clean build artifacts"

# Main target: run all checks before committing
check: generate fmt swag-fmt vet lint security test
	@echo ""
	@echo "✅ All checks passed! Ready to commit."

# Generate all code and documentation
generate: docs sqlc swag-fmt templ

# Generate swagger documentation
docs:
	@echo "🔄 Generating swagger documentation..."
	@cd app && swag init -g ./cmd/signalsd/main.go


# Generate swagger documentation
docs:
	@echo "🔄 formatting swag comments..."
	@cd app && swag fmt"

# Generate type-safe SQL code
sqlc:
	@echo "🔄 Generating SQLC code..."
	@cd app && sqlc generate

# Generate templ templates
	@echo "  make ui-build        - Build UI binary"
	@echo "  make templ           - Generate templ templates"

# Format code
fmt:
	@echo "🔄 Formatting code..."
	@cd app && go fmt ./...

# Run go vet
vet:
	@echo "🔄 Running go vet..."
	@cd app && go vet ./...

# Run staticcheck linter
lint:
	@echo "🔄 Running staticcheck..."
	@cd app && staticcheck ./...

# Run security analysis
security:
	@echo "🔄 Running security analysis..."
	@cd app && gosec -exclude-generated ./...

# Run tests
test:
	@echo "🔄 Running tests..."
	@cd app && go test ./...
	@cd app && go test -v -tags=integration ./test/integration/

# Clean build artifacts
clean:
	@echo "🔄 Cleaning..."
	@cd app && go clean -cache -testcache
	@rm appu/signalsd

psql: 
	@echo "🔄 running psql on local postgres db "
	psql ${DATABASE_URL}

# UI development commands
ui-build:
	@echo "🔄 Building UI binary..."
	@sh -c "cd app && go build -o signalsd-ui ./cmd/signalsd-ui/main.go"
